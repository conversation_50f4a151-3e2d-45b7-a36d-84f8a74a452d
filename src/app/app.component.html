<!-- <div class="relative z-10 flex flex-col items-center h-screen shadow-xl"> -->
<div class="relative z-10 flex flex-col items-center h-screen p-6 bg-gray-700 shadow-xl">
  <div class="relative z-0 flex justify-center flex-1 overflow-auto bg-white rounded-3xl">
    <router-outlet></router-outlet>
  </div>

  <!-- <div class="absolute top-0 w-full h-6 backdrop-blur-sm"></div>
  <div class="absolute bottom-0 w-full h-6 backdrop-blur-sm"></div>
  <div class="absolute right-0 w-6 h-full backdrop-blur-sm"></div>
  <div class="absolute left-0 w-6 h-full backdrop-blur-sm"></div> -->

  <!-- <div class="absolute w-6 h-6 bg-amber-900/60 backdrop-blur-sm top-6 right-6">
    <div class="bg-white rounded-tr-full size-full"></div>
  </div>

  <div class="absolute w-6 h-6 bg-amber-900/60 backdrop-blur-sm bottom-6 right-6">
    <div class="bg-white rounded-br-full size-full"></div>
  </div>

  <div class="absolute w-6 h-6 bg-amber-900/60 backdrop-blur-sm bottom-6 left-6">
    <div class="bg-white rounded-bl-full size-full"></div>
  </div>

  <div class="absolute w-6 h-6 bg-amber-900/60 backdrop-blur-sm top-6 left-6">
    <div class="bg-white rounded-tl-full size-full"></div>
  </div> -->


  <div class="absolute z-20 flex gap-3 bottom-10">
    <button
      class="flex items-center justify-center px-4 py-3 transition-all duration-500 border rounded-full shadow-2xl liquid-glass-btn backdrop-blur-sm bg-gradient-to-br from-white/25 to-white/5 border-white/30 hover:shadow-white/30 hover:bg-gradient-to-br hover:from-white/35 hover:to-white/10 hover:-translate-y-2 hover:scale-110 active:scale-95 active:translate-y-0"
      style="box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), inset 0 2px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(255, 255, 255, 0.1), 0 6px 20px rgba(255, 255, 255, 0.15);">
      <div class="size-6"><ng-icon name="lucideSunMedium" class="text-2xl transition-all duration-300"></ng-icon></div>
    </button>
    <button
      class="flex items-center justify-center gap-2 px-6 py-3 transition-all duration-500 border rounded-full shadow-2xl liquid-glass-btn backdrop-blur-sm bg-gradient-to-br from-white/25 to-white/5 border-white/30 hover:shadow-white/30 hover:bg-gradient-to-br hover:from-white/35 hover:to-white/10 hover:-translate-y-2 hover:scale-110 active:scale-95 active:translate-y-0"
      style="box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), inset 0 2px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(255, 255, 255, 0.1), 0 6px 20px rgba(255, 255, 255, 0.15); transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), padding 0.4s cubic-bezier(0.4, 0, 0.2, 1), transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.5s ease;">
      <img src="../assets/newlogoX.png" class="transition-all duration-300 size-8" />

      <span style="font-family: 'Conduit 2 Italics BRK', sans-serif; font-style: italic;"
        class="text-3xl font-semibold text-black bg-gradient-to-r from-white to-white/80 bg-clip-text drop-shadow-sm">
        <span class="static-part">{{ staticPart }}</span><span
          class="glitch-text"
          [class.glitching]="isGlitching">{{ dynamicPart }}</span>
      </span>

    </button>
    <button
      class="flex items-center justify-center p-3 px-4 transition-all duration-500 border rounded-full shadow-2xl liquid-glass-btn backdrop-blur-sm bg-gradient-to-br from-white/25 to-white/5 border-white/30 hover:shadow-white/30 hover:bg-gradient-to-br hover:from-white/35 hover:to-white/10 hover:-translate-y-2 hover:scale-110 active:scale-95 active:translate-y-0"
      style="box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), inset 0 2px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(255, 255, 255, 0.1), 0 6px 20px rgba(255, 255, 255, 0.15);">
      <div class="size-6"><ng-icon name="lucideMenu" class="text-2xl transition-all duration-300"></ng-icon></div>

    </button>
  </div>
</div>