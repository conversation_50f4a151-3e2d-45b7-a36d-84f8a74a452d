import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('./modules/home/<USER>').then((m) => m.HomeModule),
  },
  {
    path: 'docs',
    loadChildren: () =>
      import('./modules/docs/docs.module').then((m) => m.DocsModule),
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule { }
