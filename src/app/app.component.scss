.static-part {
  display: inline-block;
}

.glitch-text {
  position: relative;
  display: inline-block;
  min-width: 0;
  width: auto;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.1s ease,
              filter 0.1s ease,
              letter-spacing 0.3s ease;
  will-change: width, transform, filter;
}

.glitch-text.glitching {
  animation: glitch 0.1s infinite;
}

@keyframes glitch {
  0% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: translate(-2px, 2px);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(180deg);
  }
  30% {
    transform: translate(2px, 2px);
    filter: hue-rotate(270deg);
  }
  40% {
    transform: translate(2px, -2px);
    filter: hue-rotate(360deg);
  }
  50% {
    transform: translate(-2px, 2px);
    filter: hue-rotate(90deg);
  }
  60% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(180deg);
  }
  70% {
    transform: translate(2px, 2px);
    filter: hue-rotate(270deg);
  }
  80% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(360deg);
  }
  90% {
    transform: translate(2px, 2px);
    filter: hue-rotate(90deg);
  }
  100% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
}

.glitch-text.glitching::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, white, rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: glitch-shadow 0.1s infinite;
  z-index: -1;
}

@keyframes glitch-shadow {
  0% {
    transform: translate(0);
    opacity: 0.8;
  }
  25% {
    transform: translate(2px, 0);
    opacity: 0.6;
  }
  50% {
    transform: translate(-2px, 2px);
    opacity: 0.4;
  }
  75% {
    transform: translate(2px, -2px);
    opacity: 0.6;
  }
  100% {
    transform: translate(0);
    opacity: 0.8;
  }
}

/* Enhanced Liquid Glass Button Effects */
.liquid-glass-btn {
  position: relative;
  overflow: hidden;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
              box-shadow 0.5s ease,
              background 0.3s ease,
              border-color 0.3s ease !important;
  will-change: width, padding, transform, box-shadow;

  // Liquid glass shimmer effect
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 70%
    );
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    transition: transform 0.8s cubic-bezier(0.19, 1, 0.22, 1);
    pointer-events: none;
    z-index: 1;
  }

  // Floating particles effect
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 20% 80%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 255, 255, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 50%
    );
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 0;
  }

  // Enhanced hover effects
  &:hover {
    &::before {
      transform: translateX(100%) translateY(100%) rotate(45deg);
    }

    &::after {
      opacity: 1;
    }

    // Ripple effect on hover
    background-image: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(255, 255, 255, 0.15) 0%,
      transparent 50%
    );
  }

  // Active state with liquid deformation
  &:active {
    transform: scale(0.95) translateY(2px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                inset 0 -1px 0 rgba(255, 255, 255, 0.1) !important;

    &::before {
      transform: translateX(0) translateY(0) rotate(45deg) scale(0.8);
    }
  }

  // Ensure content is above pseudo-elements
  > * {
    position: relative;
    z-index: 2;
  }
}

/* Liquid Glass Morphing Animation */
@keyframes liquid-morph {
  0%, 100% {
    border-radius: 50px;
  }
  25% {
    border-radius: 60px 40px 50px 30px;
  }
  50% {
    border-radius: 30px 60px 40px 50px;
  }
  75% {
    border-radius: 50px 30px 60px 40px;
  }
}

/* Floating Glass Particles */
@keyframes float-particles {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-10px) rotate(120deg);
    opacity: 0.6;
  }
  66% {
    transform: translateY(-5px) rotate(240deg);
    opacity: 0.4;
  }
}

/* Liquid Glass Glow Pulse */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),
                inset 0 2px 0 rgba(255, 255, 255, 0.4),
                inset 0 -1px 0 rgba(255, 255, 255, 0.1),
                0 6px 20px rgba(255, 255, 255, 0.15);
  }
  50% {
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2),
                inset 0 3px 0 rgba(255, 255, 255, 0.5),
                inset 0 -2px 0 rgba(255, 255, 255, 0.2),
                0 8px 30px rgba(255, 255, 255, 0.25);
  }
}

/* Enhanced Liquid Glass Button with Advanced Effects */
.liquid-glass-btn {
  // Add subtle morphing animation
  animation: glow-pulse 4s ease-in-out infinite;

  // Enhanced glass refraction effect
  background-image:
    linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    linear-gradient(225deg, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    linear-gradient(315deg, rgba(255, 255, 255, 0.08) 0%, transparent 50%);

  &:hover {
    animation: glow-pulse 2s ease-in-out infinite, liquid-morph 3s ease-in-out infinite;

    // Enhanced hover glow
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25),
                inset 0 3px 0 rgba(255, 255, 255, 0.6),
                inset 0 -2px 0 rgba(255, 255, 255, 0.3),
                0 10px 40px rgba(255, 255, 255, 0.35),
                0 0 20px rgba(255, 255, 255, 0.2) !important;
  }

  // Add floating particles on hover
  &:hover::after {
    animation: float-particles 2s ease-in-out infinite;
  }
}

/* Text Shine Effects */
.text-shine {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 20%,
      rgba(255, 255, 255, 0.8) 50%,
      rgba(255, 255, 255, 0.4) 80%,
      transparent 100%
    );
    animation: shine-sweep 3s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
  }
}

@keyframes shine-sweep {
  0% {
    left: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* Pulsing Glow Shine Effect */
.text-glow-shine {
  position: relative;
  text-shadow:
    0 0 5px rgba(255, 255, 255, 0.5),
    0 0 10px rgba(255, 255, 255, 0.3),
    0 0 15px rgba(255, 255, 255, 0.2),
    0 0 20px rgba(255, 255, 255, 0.1);
  animation: glow-pulse-text 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse-text {
  from {
    text-shadow:
      0 0 5px rgba(255, 255, 255, 0.5),
      0 0 10px rgba(255, 255, 255, 0.3),
      0 0 15px rgba(255, 255, 255, 0.2),
      0 0 20px rgba(255, 255, 255, 0.1);
  }
  to {
    text-shadow:
      0 0 10px rgba(255, 255, 255, 0.8),
      0 0 20px rgba(255, 255, 255, 0.6),
      0 0 30px rgba(255, 255, 255, 0.4),
      0 0 40px rgba(255, 255, 255, 0.2);
  }
}

/* Shimmer Shine Effect */
.text-shimmer {
  position: relative;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 1) 25%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 1) 75%,
    rgba(255, 255, 255, 0.8) 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer-move 2s linear infinite;
}

@keyframes shimmer-move {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Radial Shine Effect */
.text-radial-shine {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(255, 255, 255, 0.4) 30%,
      transparent 70%
    );
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: radial-shine-expand 2.5s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
  }
}

@keyframes radial-shine-expand {
  0%, 100% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  50% {
    width: 150%;
    height: 150%;
    opacity: 1;
  }
}

/* Button smooth width transition - keeping original for compatibility */
button {
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
              box-shadow 0.5s ease !important;
  will-change: width, padding, transform;
}