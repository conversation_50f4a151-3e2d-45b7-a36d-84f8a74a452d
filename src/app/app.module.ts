import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { NgIconsModule } from '@ng-icons/core';
import { lucideX, lucideGithub, lucideMenu, lucideSunMedium } from '@ng-icons/lucide';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    NgIconsModule.withIcons({ lucideX, lucideGithub, lucideMenu, lucideSunMedium })
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
