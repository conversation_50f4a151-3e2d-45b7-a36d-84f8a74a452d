import { Component, OnInit, OnDestroy } from '@angular/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'emreartz.com';
  staticPart = 'emre';
  dynamicPart = 'artz';
  isGlitching = false;
  private intervalId: any;

  ngOnInit() {
    this.startGlitchCycle();
  }

  ngOnDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  private startGlitchCycle() {
    this.intervalId = setInterval(() => {
      this.triggerGlitch();
    }, 10000);
  }

  private triggerGlitch() {
    this.isGlitching = true;

    // Glitch efekti süresi boyunca rastgele karakterler göster
    const glitchDuration = 500; // 0.5 saniye
    const glitchInterval = 50; // Her 50ms'de bir değiştir
    const targetDynamicPart = this.dynamicPart === 'artz' ? 'maras' : 'artz';

    let glitchTimer = 0;
    const glitchIntervalId = setInterval(() => {
      if (glitchTimer < glitchDuration) {
        // Sadece dinamik kısmı glitch yap
        this.dynamicPart = this.generateGlitchText(targetDynamicPart);
        glitchTimer += glitchInterval;
      } else {
        // Glitch bitince hedef metni göster
        this.dynamicPart = targetDynamicPart;
        this.isGlitching = false;
        clearInterval(glitchIntervalId);
      }
    }, glitchInterval);
  }

  private generateGlitchText(targetText: string): string {
    const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?~`';
    const originalChars = 'abcdefghijklmnopqrstuvwxyz';

    return targetText.split('').map((char) => {
      const random = Math.random();
      if (random < 0.3) {
        // %30 ihtimalle glitch karakter
        return glitchChars[Math.floor(Math.random() * glitchChars.length)];
      } else if (random < 0.6) {
        // %30 ihtimalle rastgele harf
        return originalChars[Math.floor(Math.random() * originalChars.length)];
      } else {
        // %40 ihtimalle orijinal karakter
        return char;
      }
    }).join('');
  }
}
